// src/middlewares/rateLimitMiddleware.ts
import rateLimit from 'express-rate-limit';
import { tFromRequest } from '../i18n';
import { logger } from '../utils/logger';
import { MyRequest } from '../types/customRequest';

/**
 * 创建统一的限速中间件
 */
export const createRateLimit = (options: {
  windowMs: number;
  max: number;
  message: string;
  skipSuccessfulRequests?: boolean;
  keyGenerator?: (req: any) => string;
}) => {
  return rateLimit({
    windowMs: options.windowMs,
    max: options.max,
    message: { ok: false, message: options.message },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: options.skipSuccessfulRequests || false,
    keyGenerator: options.keyGenerator,
    handler: (req, res) => {
      const myReq = req as MyRequest;
      logger.error('API限速触发', {
        ip: req.ip,
        path: req.path,
        userAgent: req.headers['user-agent'],
        walletAddress: myReq.user?.walletAddress
      });

      res.status(429).json({
        ok: false,
        message: options.message
      });
    }
  });
};

/**
 * 认证相关接口限速配置
 */

// /api/web3-auth/nonce - 获取签名随机数
export const nonceRateLimit = createRateLimit({
  windowMs: 5 * 60 * 1000, // 5分钟
  max: 20, // 每5分钟最多20次
  message: "获取签名随机数过于频繁，请稍后再试"
});

// /api/web3-auth/login - Web3登录验证
export const loginRateLimit = createRateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 20, // 每15分钟最多20次登录尝试
  message: "登录尝试过于频繁，请稍后再试"
});

/**
 * 支付相关接口限速配置
 */

// /api/phrs-payment/purchase - PHRS支付
export const phrsPaymentRateLimit = createRateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 10, // 每分钟最多10次支付创建
  message: "支付请求过于频繁，请稍后再试"
});

/**
 * 钱包相关接口限速配置
 */

// /api/wallet/batch-update-resources - 批量更新资源
export const batchUpdateResourcesRateLimit = createRateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 30, // 每分钟最多30次（游戏操作相对频繁）
  message: "批量更新资源过于频繁，请稍后再试"
});

/**
 * 道具和任务相关接口限速配置
 */

// /api/iap/boosters/use - 使用道具
export const useBoosterRateLimit = createRateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 30, // 每分钟最多30次
  message: "使用道具过于频繁，请稍后再试"
});

// /api/new-tasks/claim - 领取任务奖励
export const claimTaskRateLimit = createRateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 50, // 每分钟最多50次
  message: "领取任务奖励过于频繁，请稍后再试"
});

/**
 * 基于用户钱包地址的限速（用于需要用户认证的接口）
 */
export const createUserBasedRateLimit = (options: {
  windowMs: number;
  max: number;
  message: string;
  skipSuccessfulRequests?: boolean;
}) => {
  return createRateLimit({
    ...options,
    keyGenerator: (req) => {
      // 优先使用钱包地址，如果没有则使用IP
      const myReq = req as MyRequest;
      const walletAddress = myReq.user?.walletAddress;
      return walletAddress ? `wallet:${walletAddress}` : `ip:${req.ip}`;
    }
  });
};

/**
 * 用户认证相关的限速中间件
 */

// 基于用户的PHRS支付限速
export const userPhrsPaymentRateLimit = createUserBasedRateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 30, // 每分钟最多30次
  message: "支付请求过于频繁，请稍后再试"
});

// 基于用户的批量更新资源限速
export const userBatchUpdateResourcesRateLimit = createUserBasedRateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 30, // 每分钟最多30次
  message: "批量更新资源过于频繁，请稍后再试"
});

// 基于用户的使用道具限速
export const userUseBoosterRateLimit = createUserBasedRateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 30, // 每分钟最多30次
  message: "使用道具过于频繁，请稍后再试"
});

// 基于用户的领取任务奖励限速
export const userClaimTaskRateLimit = createUserBasedRateLimit({
  windowMs: 1 * 60 * 1000, // 1分钟
  max: 50, // 每分钟最多10次
  message: "领取任务奖励过于频繁，请稍后再试"
});
