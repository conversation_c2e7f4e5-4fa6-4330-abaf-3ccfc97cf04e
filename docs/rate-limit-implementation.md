# 接口限速实现文档

## 概述

本文档记录了为指定接口添加限速功能的实现详情。所有限速功能基于 `express-rate-limit` 中间件实现，并创建了统一的限速配置管理。

## 实现的限速接口

### 1. 认证相关接口

#### `/api/web3-auth/nonce` - 获取签名随机数
- **限制**: 5分钟内最多10次请求
- **实现**: 使用 `nonceRateLimit` 中间件
- **原因**: 防止恶意获取大量nonce值

#### `/api/web3-auth/login` - Web3登录验证  
- **限制**: 15分钟内最多20次登录尝试
- **实现**: 使用 `loginRateLimit` 中间件
- **原因**: 防止暴力破解和恶意登录尝试

### 2. 支付相关接口

#### `/api/phrs-payment/purchase` - PHRS支付
- **限制**: 1分钟内最多5次支付请求
- **实现**: 使用 `userPhrsPaymentRateLimit` 中间件（基于用户钱包地址）
- **原因**: 防止重复支付和恶意消费

### 3. 钱包相关接口

#### `/api/wallet/batch-update-resources` - 批量更新资源
- **限制**: 1分钟内最多30次请求
- **实现**: 使用 `userBatchUpdateResourcesRateLimit` 中间件（基于用户钱包地址）
- **原因**: 游戏操作相对频繁，但需要防止异常刷取

### 4. 道具和任务相关接口

#### `/api/iap/boosters/use` - 使用道具
- **限制**: 1分钟内最多20次请求
- **实现**: 使用 `userUseBoosterRateLimit` 中间件（基于用户钱包地址）
- **原因**: 防止道具滥用

#### `/api/new-tasks/claim` - 领取任务奖励
- **限制**: 1分钟内最多10次请求
- **实现**: 使用 `userClaimTaskRateLimit` 中间件（基于用户钱包地址）
- **原因**: 防止重复领取奖励

## 技术实现

### 1. 统一限速中间件

创建了 `src/middlewares/rateLimitMiddleware.ts` 文件，包含：

- `createRateLimit()`: 通用限速中间件创建函数
- `createUserBasedRateLimit()`: 基于用户的限速中间件创建函数
- 预配置的各种限速中间件

### 2. 限速策略

#### IP限速 vs 用户限速
- **IP限速**: 用于公开接口（如认证接口）
- **用户限速**: 用于需要认证的接口，基于钱包地址进行限制

#### 限速参数设计原则
- **认证接口**: 相对宽松，防止正常用户受影响
- **支付接口**: 严格限制，防止财务风险
- **游戏操作**: 适中限制，平衡用户体验和安全性

### 3. 错误处理

所有限速中间件统一返回：
```json
{
  "ok": false,
  "message": "具体的限速提示信息"
}
```

状态码：`429 Too Many Requests`

### 4. 日志记录

限速触发时会记录以下信息：
- IP地址
- 请求路径
- User-Agent
- 用户钱包地址（如果有）

## 文件修改清单

### 新增文件
- `src/middlewares/rateLimitMiddleware.ts` - 统一限速中间件配置
- `scripts/test-rate-limits.js` - 完整的限速测试脚本
- `scripts/simple-rate-limit-test.js` - 简单的限速测试脚本
- `docs/rate-limit-implementation.md` - 本文档

### 修改文件
- `src/routes/web3AuthRoutes.ts` - 添加认证接口限速
- `src/routes/phrsPaymentRoutes.ts` - 添加支付接口限速
- `src/routes/walletRoutes.ts` - 添加钱包接口限速
- `src/routes/iapRoutes.ts` - 添加道具接口限速
- `src/routes/newTaskRoutes.ts` - 添加任务接口限速

## 测试方法

### 1. 简单测试
```bash
node scripts/simple-rate-limit-test.js
```

### 2. 完整测试
```bash
node scripts/test-rate-limits.js
```

### 3. 指定服务器测试
```bash
node scripts/simple-rate-limit-test.js http://your-server:port
```

## 监控和维护

### 1. 监控指标
- 限速触发频率
- 被限速的IP/用户
- 限速对正常用户的影响

### 2. 调整建议
- 根据实际使用情况调整限速参数
- 监控误杀正常用户的情况
- 考虑添加白名单机制

### 3. 扩展性
- 可以轻松添加新的限速接口
- 支持不同的限速策略
- 可以集成Redis进行分布式限速

## 注意事项

1. **开发环境**: 限速在开发环境中也会生效，注意调试时的影响
2. **测试环境**: 建议在测试环境中验证限速参数的合理性
3. **生产环境**: 上线前确保限速参数不会影响正常用户使用
4. **依赖**: 确保 `express-rate-limit` 包已正确安装

## 后续优化建议

1. **Redis集成**: 对于分布式部署，考虑使用Redis存储限速数据
2. **动态配置**: 支持运行时动态调整限速参数
3. **白名单机制**: 为特殊用户或IP提供白名单功能
4. **更细粒度控制**: 基于用户等级、VIP状态等进行差异化限速
