#!/usr/bin/env node

/**
 * 简单的限速测试脚本
 * 测试 /api/web3-auth/nonce 接口的限速功能
 */

const axios = require('axios');

const BASE_URL = process.argv[2] || 'http://localhost:3000';
const TEST_ENDPOINT = '/api/web3-auth/nonce';

async function testNonceRateLimit() {
  console.log('🧪 测试 /api/web3-auth/nonce 接口限速功能');
  console.log(`🌐 服务器: ${BASE_URL}`);
  console.log('📝 限制: 5分钟内最多10次请求');
  console.log('-'.repeat(50));

  const testData = {
    walletAddress: '******************************************'
  };

  let successCount = 0;
  let rateLimitCount = 0;
  let errorCount = 0;

  // 发送15个请求，应该有5个被限速
  for (let i = 1; i <= 15; i++) {
    try {
      const response = await axios.post(`${BASE_URL}${TEST_ENDPOINT}`, testData, {
        timeout: 5000
      });
      
      successCount++;
      console.log(`✅ 请求 ${i}: 成功 (${response.status})`);
      
    } catch (error) {
      if (error.response && error.response.status === 429) {
        rateLimitCount++;
        console.log(`🚫 请求 ${i}: 被限速 (429) - ${error.response.data?.message || '请求过于频繁'}`);
      } else {
        errorCount++;
        const status = error.response?.status || 'ERROR';
        const message = error.response?.data?.message || error.message;
        console.log(`❌ 请求 ${i}: 错误 (${status}) - ${message}`);
      }
    }
    
    // 短暂延迟，避免请求过快
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log('-'.repeat(50));
  console.log('📊 测试结果:');
  console.log(`   ✅ 成功请求: ${successCount}`);
  console.log(`   🚫 限速拦截: ${rateLimitCount}`);
  console.log(`   ❌ 其他错误: ${errorCount}`);
  
  if (rateLimitCount > 0) {
    console.log('\n🎉 限速功能正常工作！');
  } else {
    console.log('\n⚠️  警告: 没有触发限速，请检查配置');
  }
}

// 运行测试
if (require.main === module) {
  testNonceRateLimit().catch(console.error);
}
