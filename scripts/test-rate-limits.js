#!/usr/bin/env node

/**
 * 测试接口限速功能
 * 
 * 使用方法:
 * node scripts/test-rate-limits.js
 * 
 * 或者指定服务器地址:
 * node scripts/test-rate-limits.js http://localhost:3000
 */

const axios = require('axios');

// 默认服务器地址
const BASE_URL = process.argv[2] || 'http://localhost:3000';

// 测试配置
const TEST_CONFIG = {
  '/api/web3-auth/nonce': {
    method: 'POST',
    data: { walletAddress: '******************************************' },
    limit: 10,
    windowMs: 5 * 60 * 1000, // 5分钟
    description: '获取签名随机数'
  },
  '/api/web3-auth/login': {
    method: 'POST',
    data: { 
      walletAddress: '******************************************',
      signature: 'test_signature',
      message: 'test_message'
    },
    limit: 20,
    windowMs: 15 * 60 * 1000, // 15分钟
    description: 'Web3登录'
  }
};

// 需要认证的接口测试配置
const AUTH_TEST_CONFIG = {
  '/api/phrs-payment/purchase': {
    method: 'POST',
    data: { productId: 1 },
    limit: 5,
    windowMs: 1 * 60 * 1000, // 1分钟
    description: 'PHRS支付'
  },
  '/api/wallet/batch-update-resources': {
    method: 'POST',
    data: { 
      gem: 100,
      pendingMilk: 50
    },
    limit: 30,
    windowMs: 1 * 60 * 1000, // 1分钟
    description: '批量更新资源'
  },
  '/api/iap/boosters/use': {
    method: 'POST',
    data: { boosterId: 1 },
    limit: 20,
    windowMs: 1 * 60 * 1000, // 1分钟
    description: '使用道具'
  },
  '/api/new-tasks/claim': {
    method: 'POST',
    data: { taskId: 1 },
    limit: 10,
    windowMs: 1 * 60 * 1000, // 1分钟
    description: '领取任务奖励'
  }
};

/**
 * 延迟函数
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 测试单个接口的限速功能
 */
async function testRateLimit(endpoint, config, headers = {}) {
  console.log(`\n🧪 测试接口: ${endpoint}`);
  console.log(`📝 描述: ${config.description}`);
  console.log(`⏱️  限制: ${config.limit}次/${config.windowMs/1000}秒`);
  
  let successCount = 0;
  let rateLimitCount = 0;
  
  try {
    // 快速发送请求，测试限速
    const requests = [];
    for (let i = 0; i < config.limit + 5; i++) {
      requests.push(
        axios({
          method: config.method,
          url: `${BASE_URL}${endpoint}`,
          data: config.data,
          headers,
          timeout: 5000
        }).then(response => {
          successCount++;
          return { success: true, status: response.status };
        }).catch(error => {
          if (error.response && error.response.status === 429) {
            rateLimitCount++;
            return { success: false, status: 429, rateLimited: true };
          }
          return { success: false, status: error.response?.status || 'ERROR', error: error.message };
        })
      );
    }
    
    const results = await Promise.all(requests);
    
    console.log(`✅ 成功请求: ${successCount}`);
    console.log(`🚫 限速拦截: ${rateLimitCount}`);
    console.log(`❌ 其他错误: ${results.length - successCount - rateLimitCount}`);
    
    // 分析结果
    if (rateLimitCount > 0) {
      console.log(`✅ 限速功能正常工作！`);
    } else {
      console.log(`⚠️  警告: 没有触发限速，可能配置有问题`);
    }
    
    // 显示详细结果
    const statusCounts = {};
    results.forEach(result => {
      const status = result.rateLimited ? '429 (Rate Limited)' : result.status;
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });
    
    console.log('📊 状态码统计:');
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`   ${status}: ${count}次`);
    });
    
  } catch (error) {
    console.error(`❌ 测试失败: ${error.message}`);
  }
}

/**
 * 获取测试用的认证token
 */
async function getTestToken() {
  try {
    // 这里应该实现获取有效token的逻辑
    // 为了测试，我们返回一个模拟的token
    console.log('⚠️  注意: 使用模拟token进行测试，某些接口可能返回401');
    return 'Bearer test_token_for_rate_limit_testing';
  } catch (error) {
    console.error('获取测试token失败:', error.message);
    return null;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始测试接口限速功能');
  console.log(`🌐 服务器地址: ${BASE_URL}`);
  console.log('=' .repeat(60));
  
  // 测试不需要认证的接口
  console.log('\n📋 测试公开接口限速功能');
  for (const [endpoint, config] of Object.entries(TEST_CONFIG)) {
    await testRateLimit(endpoint, config);
    await delay(1000); // 等待1秒再测试下一个接口
  }
  
  // 获取认证token
  const token = await getTestToken();
  
  if (token) {
    console.log('\n📋 测试需要认证的接口限速功能');
    const authHeaders = {
      'Authorization': token,
      'Content-Type': 'application/json'
    };
    
    for (const [endpoint, config] of Object.entries(AUTH_TEST_CONFIG)) {
      await testRateLimit(endpoint, config, authHeaders);
      await delay(1000); // 等待1秒再测试下一个接口
    }
  } else {
    console.log('\n⚠️  跳过需要认证的接口测试（无法获取有效token）');
  }
  
  console.log('\n' + '=' .repeat(60));
  console.log('✅ 限速功能测试完成');
  console.log('\n💡 提示:');
  console.log('   - 如果看到429状态码，说明限速功能正常工作');
  console.log('   - 如果没有429状态码，请检查限速配置');
  console.log('   - 某些接口可能因为业务逻辑返回其他错误码，这是正常的');
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, testRateLimit };
